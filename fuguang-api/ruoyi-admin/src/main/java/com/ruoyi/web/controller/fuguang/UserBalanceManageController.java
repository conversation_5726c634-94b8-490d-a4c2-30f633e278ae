package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户余额管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController("userBalanceManageController")
@RequestMapping("/fuguang/balance")
public class UserBalanceManageController extends BaseController
{
    @Autowired
    private IUserBalanceService userBalanceService;

    @Autowired
    private IBalanceRecordService balanceRecordService;

    /**
     * 查询用户余额列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserBalance userBalance)
    {
        startPage();
        List<UserBalance> list = userBalanceService.selectUserBalanceList(userBalance);
        return getDataTable(list);
    }

    /**
     * 导出用户余额列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:export')")
    @Log(title = "用户余额", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserBalance userBalance)
    {
        List<UserBalance> list = userBalanceService.selectUserBalanceList(userBalance);
        ExcelUtil<UserBalance> util = new ExcelUtil<UserBalance>(UserBalance.class);
        util.exportExcel(response, list, "用户余额数据");
    }

    /**
     * 获取用户余额详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:query')")
    @GetMapping(value = "/{balanceId}")
    public AjaxResult getInfo(@PathVariable("balanceId") Long balanceId)
    {
        return success(userBalanceService.selectUserBalanceByBalanceId(balanceId));
    }

    /**
     * 根据用户ID获取用户余额详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:query')")
    @GetMapping(value = "/user/{userId}")
    public AjaxResult getInfoByUserId(@PathVariable("userId") Long userId)
    {
        UserBalance userBalance = userBalanceService.selectUserBalanceByUserId(userId);
        if (userBalance == null) {
            userBalance = userBalanceService.initUserBalance(userId);
        }
        return success(userBalance);
    }

    /**
     * 手动调整用户余额
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:adjust')")
    @Log(title = "调整用户余额", businessType = BusinessType.UPDATE)
    @PostMapping("/adjust")
    public AjaxResult adjustBalance(@RequestParam Long userId, 
                                   @RequestParam BigDecimal amount, 
                                   @RequestParam String type,
                                   @RequestParam String reason)
    {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return error("调整金额必须大于0");
        }

        boolean result;
        if ("increase".equals(type)) {
            // 增加余额
            result = balanceRecordService.addIncome(userId, amount, "admin_adjust",
                    null, null, "管理员调整：" + reason, "3");
        } else if ("decrease".equals(type)) {
            // 减少余额
            result = balanceRecordService.addExpense(userId, amount, "admin_adjust",
                    null, null, "管理员调整：" + reason);
        } else {
            return error("调整类型错误");
        }

        if (result) {
            return success("余额调整成功");
        } else {
            return error("余额调整失败，请检查用户余额是否充足");
        }
    }

    /**
     * 冻结用户余额
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:freeze')")
    @Log(title = "冻结用户余额", businessType = BusinessType.UPDATE)
    @PostMapping("/freeze")
    public AjaxResult freezeBalance(@RequestParam Long userId, @RequestParam BigDecimal amount)
    {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return error("冻结金额必须大于0");
        }

        boolean result = userBalanceService.freezeBalance(userId, amount);
        if (result) {
            return success("余额冻结成功");
        } else {
            return error("余额冻结失败，请检查用户可用余额是否充足");
        }
    }

    /**
     * 解冻用户余额
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:unfreeze')")
    @Log(title = "解冻用户余额", businessType = BusinessType.UPDATE)
    @PostMapping("/unfreeze")
    public AjaxResult unfreezeBalance(@RequestParam Long userId, @RequestParam BigDecimal amount)
    {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return error("解冻金额必须大于0");
        }

        boolean result = userBalanceService.unfreezeBalance(userId, amount);
        if (result) {
            return success("余额解冻成功");
        } else {
            return error("余额解冻失败，请检查用户冻结余额是否充足");
        }
    }

    /**
     * 获取用户余额统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getBalanceStatistics()
    {
        List<UserBalance> allBalances = userBalanceService.selectUserBalanceList(new UserBalance());
        
        Map<String, Object> statistics = new HashMap<>();
        BigDecimal totalBalance = BigDecimal.ZERO;
        BigDecimal totalAvailableBalance = BigDecimal.ZERO;
        BigDecimal totalFrozenBalance = BigDecimal.ZERO;
        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal totalWithdraw = BigDecimal.ZERO;
        int userCount = allBalances.size();
        
        for (UserBalance balance : allBalances) {
            totalBalance = totalBalance.add(balance.getTotalBalance());
            totalAvailableBalance = totalAvailableBalance.add(balance.getAvailableBalance());
            totalFrozenBalance = totalFrozenBalance.add(balance.getFrozenBalance());
            totalIncome = totalIncome.add(balance.getTotalIncome());
            totalWithdraw = totalWithdraw.add(balance.getTotalWithdraw());
        }
        
        statistics.put("userCount", userCount);
        statistics.put("totalBalance", totalBalance);
        statistics.put("totalAvailableBalance", totalAvailableBalance);
        statistics.put("totalFrozenBalance", totalFrozenBalance);
        statistics.put("totalIncome", totalIncome);
        statistics.put("totalWithdraw", totalWithdraw);
        statistics.put("netIncome", totalIncome.subtract(totalWithdraw));
        
        return success(statistics);
    }
}
