package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 余额变动记录管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController("balanceRecordManageController")
@RequestMapping("/fuguang/balanceRecord")
public class BalanceRecordManageController extends BaseController
{
    @Autowired
    private IBalanceRecordService balanceRecordService;

    @Autowired
    private IUserBalanceService userBalanceService;

    /**
     * 查询余额变动记录列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(BalanceRecord balanceRecord)
    {
        startPage();
        List<BalanceRecord> list = balanceRecordService.selectBalanceRecordList(balanceRecord);
        return getDataTable(list);
    }

    /**
     * 导出余额变动记录列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:export')")
    @Log(title = "余额变动记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BalanceRecord balanceRecord)
    {
        List<BalanceRecord> list = balanceRecordService.selectBalanceRecordList(balanceRecord);
        ExcelUtil<BalanceRecord> util = new ExcelUtil<BalanceRecord>(BalanceRecord.class);
        util.exportExcel(response, list, "余额变动记录数据");
    }

    /**
     * 获取余额变动记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:query')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return success(balanceRecordService.selectBalanceRecordByRecordId(recordId));
    }



    /**
     * 新增余额变动记录
     * 注意：管理员手动添加余额记录时，会同步更新用户余额和佣金账单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:add')")
    @Log(title = "余额变动记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BalanceRecord balanceRecord)
    {
        // 验证必要字段
        if (balanceRecord.getUserId() == null) {
            return error("用户ID不能为空");
        }
        if (balanceRecord.getChangeAmount() == null || balanceRecord.getChangeAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return error("变动金额必须大于0");
        }
        if (balanceRecord.getChangeType() == null) {
            return error("变动类型不能为空");
        }

        // 根据变动类型处理余额
        boolean result = false;
        if ("1".equals(balanceRecord.getChangeType())) {
            // 收入 - 增加余额
            result = balanceRecordService.addIncome(
                balanceRecord.getUserId(),
                balanceRecord.getChangeAmount(),
                balanceRecord.getBusinessType() != null ? balanceRecord.getBusinessType() : "admin_adjust",
                balanceRecord.getBusinessId(),
                balanceRecord.getBusinessNo(),
                balanceRecord.getDescription() != null ? balanceRecord.getDescription() : "管理员手动调整",
                balanceRecord.getIncomeType()
            );
        } else if ("2".equals(balanceRecord.getChangeType())) {
            // 支出 - 减少余额
            result = balanceRecordService.addExpense(
                balanceRecord.getUserId(),
                balanceRecord.getChangeAmount(),
                balanceRecord.getBusinessType() != null ? balanceRecord.getBusinessType() : "admin_adjust",
                balanceRecord.getBusinessId(),
                balanceRecord.getBusinessNo(),
                balanceRecord.getDescription() != null ? balanceRecord.getDescription() : "管理员手动调整"
            );
        }

        if (result) {
            return success("余额记录添加成功，用户余额和佣金账单已同步更新");
        } else {
            return error("余额记录添加失败，请检查用户信息和余额是否充足");
        }
    }

    /**
     * 修改余额变动记录
     * 注意：为了保证数据一致性，只允许修改描述等非关键字段
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:edit')")
    @Log(title = "余额变动记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BalanceRecord balanceRecord)
    {
        // 获取原始记录
        BalanceRecord originalRecord = balanceRecordService.selectBalanceRecordByRecordId(balanceRecord.getRecordId());
        if (originalRecord == null) {
            return error("余额记录不存在");
        }
        // 只允许修改非关键字段，防止数据不一致
        originalRecord.setDescription(balanceRecord.getDescription());
        originalRecord.setRemark(balanceRecord.getRemark());
        int result = balanceRecordService.updateBalanceRecord(originalRecord);
        if (result > 0) {
            return success("余额记录修改成功（仅允许修改描述信息）");
        } else {
            return error("余额记录修改失败");
        }
    }

    /**
     * 获取余额变动统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getBalanceRecordStatistics(@RequestParam(required = false) String startDate,
                                                @RequestParam(required = false) String endDate,
                                                @RequestParam(required = false) String changeType,
                                                @RequestParam(required = false) String incomeType)
    {
        BalanceRecord query = new BalanceRecord();
        query.setChangeType(changeType);
        query.setIncomeType(incomeType);
        
        List<BalanceRecord> records;
        if (startDate != null && endDate != null) {
            // 这里需要扩展Service方法来支持时间范围查询
            records = balanceRecordService.selectBalanceRecordList(query);
        } else {
            records = balanceRecordService.selectBalanceRecordList(query);
        }
        
        Map<String, Object> statistics = new HashMap<>();
        BigDecimal totalIncomeAmount = BigDecimal.ZERO;
        BigDecimal totalExpenseAmount = BigDecimal.ZERO;
        BigDecimal taskCommissionAmount = BigDecimal.ZERO;
        BigDecimal recommendRewardAmount = BigDecimal.ZERO;
        BigDecimal otherIncomeAmount = BigDecimal.ZERO;
        
        int totalCount = records.size();
        int incomeCount = 0;
        int expenseCount = 0;
        
        // 按业务类型统计
        Map<String, Integer> businessTypeCount = new HashMap<>();
        Map<String, BigDecimal> businessTypeAmount = new HashMap<>();
        
        for (BalanceRecord record : records) {
            if ("1".equals(record.getChangeType())) {
                // 收入
                incomeCount++;
                totalIncomeAmount = totalIncomeAmount.add(record.getChangeAmount());
                
                // 按收入类型统计
                if ("1".equals(record.getIncomeType())) {
                    taskCommissionAmount = taskCommissionAmount.add(record.getChangeAmount());
                } else if ("2".equals(record.getIncomeType())) {
                    recommendRewardAmount = recommendRewardAmount.add(record.getChangeAmount());
                } else if ("3".equals(record.getIncomeType())) {
                    otherIncomeAmount = otherIncomeAmount.add(record.getChangeAmount());
                }
            } else if ("2".equals(record.getChangeType())) {
                // 支出
                expenseCount++;
                totalExpenseAmount = totalExpenseAmount.add(record.getChangeAmount());
            }
            
            // 按业务类型统计
            String businessType = record.getBusinessType();
            businessTypeCount.put(businessType, businessTypeCount.getOrDefault(businessType, 0) + 1);
            businessTypeAmount.put(businessType, businessTypeAmount.getOrDefault(businessType, BigDecimal.ZERO).add(record.getChangeAmount()));
        }
        
        statistics.put("totalCount", totalCount);
        statistics.put("incomeCount", incomeCount);
        statistics.put("expenseCount", expenseCount);
        statistics.put("totalIncomeAmount", totalIncomeAmount);
        statistics.put("totalExpenseAmount", totalExpenseAmount);
        statistics.put("netAmount", totalIncomeAmount.subtract(totalExpenseAmount));
        statistics.put("taskCommissionAmount", taskCommissionAmount);
        statistics.put("recommendRewardAmount", recommendRewardAmount);
        statistics.put("otherIncomeAmount", otherIncomeAmount);
        statistics.put("businessTypeCount", businessTypeCount);
        statistics.put("businessTypeAmount", businessTypeAmount);
        
        return success(statistics);
    }

    /**
     * 获取收入类型分布统计
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:statistics')")
    @GetMapping("/income-distribution")
    public AjaxResult getIncomeDistribution(@RequestParam(required = false) String startDate,
                                           @RequestParam(required = false) String endDate)
    {
        BalanceRecord query = new BalanceRecord();
        query.setChangeType("1"); // 只查询收入记录
        
        List<BalanceRecord> records = balanceRecordService.selectBalanceRecordList(query);
        
        Map<String, Object> distribution = new HashMap<>();
        BigDecimal taskCommissionTotal = BigDecimal.ZERO;
        BigDecimal recommendRewardTotal = BigDecimal.ZERO;
        BigDecimal otherIncomeTotal = BigDecimal.ZERO;
        
        int taskCommissionCount = 0;
        int recommendRewardCount = 0;
        int otherIncomeCount = 0;
        
        for (BalanceRecord record : records) {
            if ("1".equals(record.getIncomeType())) {
                taskCommissionTotal = taskCommissionTotal.add(record.getChangeAmount());
                taskCommissionCount++;
            } else if ("2".equals(record.getIncomeType())) {
                recommendRewardTotal = recommendRewardTotal.add(record.getChangeAmount());
                recommendRewardCount++;
            } else if ("3".equals(record.getIncomeType())) {
                otherIncomeTotal = otherIncomeTotal.add(record.getChangeAmount());
                otherIncomeCount++;
            }
        }
        
        BigDecimal totalAmount = taskCommissionTotal.add(recommendRewardTotal).add(otherIncomeTotal);
        
        Map<String, Object> taskCommissionMap = new HashMap<>();
        taskCommissionMap.put("amount", taskCommissionTotal);
        taskCommissionMap.put("count", taskCommissionCount);
        distribution.put("taskCommission", taskCommissionMap);

        Map<String, Object> recommendRewardMap = new HashMap<>();
        recommendRewardMap.put("amount", recommendRewardTotal);
        recommendRewardMap.put("count", recommendRewardCount);
        distribution.put("recommendReward", recommendRewardMap);

        Map<String, Object> otherIncomeMap = new HashMap<>();
        otherIncomeMap.put("amount", otherIncomeTotal);
        otherIncomeMap.put("count", otherIncomeCount);
        distribution.put("otherIncome", otherIncomeMap);

        Map<String, Object> totalMap = new HashMap<>();
        totalMap.put("amount", totalAmount);
        totalMap.put("count", records.size());
        distribution.put("total", totalMap);
        
        return success(distribution);
    }

    /**
     * 获取业务类型分布统计
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balanceRecord:statistics')")
    @GetMapping("/business-distribution")
    public AjaxResult getBusinessDistribution(@RequestParam(required = false) String startDate,
                                             @RequestParam(required = false) String endDate)
    {
        List<BalanceRecord> records = balanceRecordService.selectBalanceRecordList(new BalanceRecord());
        
        Map<String, Map<String, Object>> businessDistribution = new HashMap<>();
        
        for (BalanceRecord record : records) {
            String businessType = record.getBusinessType();
            if (!businessDistribution.containsKey(businessType)) {
                businessDistribution.put(businessType, new HashMap<>());
                businessDistribution.get(businessType).put("amount", BigDecimal.ZERO);
                businessDistribution.get(businessType).put("count", 0);
            }
            
            BigDecimal currentAmount = (BigDecimal) businessDistribution.get(businessType).get("amount");
            int currentCount = (Integer) businessDistribution.get(businessType).get("count");
            
            businessDistribution.get(businessType).put("amount", currentAmount.add(record.getChangeAmount()));
            businessDistribution.get(businessType).put("count", currentCount + 1);
        }
        
        return success(businessDistribution);
    }
}
