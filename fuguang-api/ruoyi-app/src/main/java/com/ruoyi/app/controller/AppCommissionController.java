package com.ruoyi.app.controller;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.CommissionBill;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.domain.WithdrawRecord;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.IWithdrawService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * 佣金账单管理控制器
 * 提供用户余额查询、佣金账单查询、提现申请等功能
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Api(tags = "佣金账单管理", description = "用户佣金和余额管理相关接口")
@RestController
@RequestMapping("/app/commission")
public class AppCommissionController extends BaseController
{
    @Autowired
    private ICommissionBillService commissionBillService;

    @Autowired
    private IBalanceRecordService balanceRecordService;

    @Autowired
    private IUserBalanceService userBalanceService;

    @Autowired
    private IWithdrawService withdrawService;

    /**
     * 获取用户余额信息
     * 查询当前用户的余额详情，包括可用余额、冻结余额等
     *
     * @return 用户余额信息
     */
    @ApiOperation(value = "获取用户余额信息",
                  notes = "查询当前用户的余额详情，包括可用余额、冻结余额、累计收入等信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回余额信息")
    })
    @GetMapping("/balance")
    public AjaxResult getUserBalance()
    {
        Long userId = getUserId();
        UserBalance userBalance = userBalanceService.selectUserBalanceByUserId(userId);
        if (userBalance == null) {
            userBalance = userBalanceService.initUserBalance(userId);
        }
        return success(userBalance);
    }
    /**
     * 查询佣金账单列表
     *
     * @return 佣金账单列表
     */
    @ApiOperation(value = "查询佣金账单列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回佣金账单列表")
    })
    @GetMapping("/bills")
    public TableDataInfo getCommissionBills()
    {
        Long userId = getUserId();
        startPage();
        List<CommissionBill> bills = commissionBillService.selectCommissionBillListByUserId(userId);
        return getDataTable(bills);
    }
    /**
     * 获取指定月份的佣金账单详情
     */
    @ApiOperation("获取指定月份的佣金账单详情")
    @GetMapping("/bill/{year}/{month}")
    public AjaxResult getMonthlyBill(
            @ApiParam("年份") @PathVariable Integer year,
            @ApiParam("月份") @PathVariable Integer month)
    {
        Long userId = getUserId();
        // 获取月度账单统计
        Map<String, Object> statistics = commissionBillService.getMonthlyBillStatistics(userId, year, month);
        // 获取该月的余额变动记录
        String startTime = String.format("%d-%02d-01 00:00:00", year, month);
        String endTime;
        if (month == 12) {
            endTime = String.format("%d-01-01 00:00:00", year + 1);
        } else {
            endTime = String.format("%d-%02d-01 00:00:00", year, month + 1);
        }
        List<BalanceRecord> records = balanceRecordService.selectBalanceRecordListByUserIdAndTime(userId, startTime, endTime);
        Map<String, Object> result = new HashMap<>();
        result.put("statistics", statistics);
        result.put("records", records);
        return success(result);
    }

    /**
     * 查询余额变动记录
     */
    @ApiOperation("查询余额变动记录")
    @GetMapping("/records")
    public TableDataInfo getBalanceRecords(
            @ApiParam("变动类型（1收入 2支出）") @RequestParam(required = false) String changeType,
            @ApiParam("收入类型（1任务佣金 2推荐奖励 3其他收入）") @RequestParam(required = false) String incomeType)
    {
        Long userId = getUserId();
        BalanceRecord query = new BalanceRecord();
        query.setUserId(userId);
        query.setChangeType(changeType);
        query.setIncomeType(incomeType);
        startPage();
        List<BalanceRecord> records = balanceRecordService.selectBalanceRecordList(query);
        return getDataTable(records);
    }
    /**
     * 查询提现记录
     */
    @ApiOperation("查询提现记录")
    @GetMapping("/withdraws")
    public TableDataInfo getWithdrawRecords()
    {
        Long userId = getUserId();
        startPage();
        List<WithdrawRecord> records = withdrawService.selectWithdrawRecordsByUserId(userId);
        return getDataTable(records);
    }
    /**
     * 查询提现记录详情（包含渠道信息）
     */
    @ApiOperation("查询提现记录详情")
    @GetMapping("/withdraw/{withdrawId}")
    public AjaxResult getWithdrawRecordDetail(@ApiParam("提现记录ID") @PathVariable Long withdrawId)
    {
        WithdrawRecord record = withdrawService.selectWithdrawRecordByWithdrawId(withdrawId);
        if (record == null) {
            return error("提现记录不存在");
        }
        return success(record);
    }
    /**
     * 获取收入统计（按类型）
     */
    @ApiOperation("获取收入统计")
    @GetMapping("/income-statistics")
    public AjaxResult getIncomeStatistics(
            @ApiParam("年份") @RequestParam(required = false) Integer year,
            @ApiParam("月份") @RequestParam(required = false) Integer month)
    {
        Long userId = getUserId();
        // 如果没有指定年月，使用当前年月
        if (year == null || month == null) {
            Calendar calendar = Calendar.getInstance();
            year = calendar.get(Calendar.YEAR);
            month = calendar.get(Calendar.MONTH) + 1;
        }
        Map<String, Object> statistics = commissionBillService.getMonthlyBillStatistics(userId, year, month);
        // 构建收入统计数据
        Map<String, Object> incomeStats = new HashMap<>();
        incomeStats.put("taskCommission", statistics.get("taskCommission"));
        incomeStats.put("recommendReward", statistics.get("recommendReward"));
        incomeStats.put("otherIncome", statistics.get("otherIncome"));
        incomeStats.put("totalIncome", statistics.get("totalIncome"));
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("month", month);
        result.put("incomeStatistics", incomeStats);
        return success(result);
    }

    /**
     * 获取支出统计
     */
    @ApiOperation("获取支出统计")
    @GetMapping("/expense-statistics")
    public AjaxResult getExpenseStatistics(
            @ApiParam("年份") @RequestParam(required = false) Integer year,
            @ApiParam("月份") @RequestParam(required = false) Integer month)
    {
        Long userId = getUserId();
        
        // 如果没有指定年月，使用当前年月
        if (year == null || month == null) {
            Calendar calendar = Calendar.getInstance();
            year = calendar.get(Calendar.YEAR);
            month = calendar.get(Calendar.MONTH) + 1;
        }
        
        Map<String, Object> statistics = commissionBillService.getMonthlyBillStatistics(userId, year, month);
        
        // 构建支出统计数据
        Map<String, Object> expenseStats = new HashMap<>();
        expenseStats.put("totalWithdraw", statistics.get("totalWithdraw"));
        expenseStats.put("withdrawCount", statistics.get("withdrawCount"));
        
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("month", month);
        result.put("expenseStatistics", expenseStats);
        
        return success(result);
    }
}
