package com.ruoyi.fuguang.service;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.fuguang.domain.BalanceRecord;

/**
 * 余额变动记录Service接口
 * 统一的余额变动入口，所有余额变动都必须通过此服务
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IBalanceRecordService
{
    /**
     * 查询余额变动记录
     * 
     * @param recordId 余额变动记录主键
     * @return 余额变动记录
     */
    public BalanceRecord selectBalanceRecordByRecordId(Long recordId);

    /**
     * 查询余额变动记录列表
     * 
     * @param balanceRecord 余额变动记录
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordList(BalanceRecord balanceRecord);


    /**
     * 根据用户ID和时间范围查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordListByUserIdAndTime(Long userId, String startTime, String endTime);

    /**
     * 新增余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    public int insertBalanceRecord(BalanceRecord balanceRecord);

    /**
     * 修改余额变动记录
     *
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    public int updateBalanceRecord(BalanceRecord balanceRecord);


    /**
     * 增加用户收入
     * 统一的收入增加入口，会同时更新用户余额、记录变动、更新佣金账单
     *
     * @param userId 用户ID
     * @param amount 收入金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @param incomeType 收入类型（1任务佣金 2推荐奖励 3其他收入）
     * @return 结果
     */
    public boolean addIncome(Long userId, BigDecimal amount, String businessType,
                           Long businessId, String businessNo, String description, String incomeType);

    /**
     * 增加用户支出
     * 统一的支出增加入口，会同时更新用户余额、记录变动、更新佣金账单
     *
     * @param userId 用户ID
     * @param amount 支出金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @return 结果
     */
    public boolean addExpense(Long userId, BigDecimal amount, String businessType,
                            Long businessId, String businessNo, String description);
}
