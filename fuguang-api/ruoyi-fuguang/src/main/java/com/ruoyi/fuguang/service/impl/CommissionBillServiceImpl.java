package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.CommissionBillMapper;
import com.ruoyi.fuguang.domain.CommissionBill;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 佣金账单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class CommissionBillServiceImpl implements ICommissionBillService 
{
    @Autowired
    private CommissionBillMapper commissionBillMapper;

    /**
     * 查询佣金账单
     * 
     * @param billId 佣金账单主键
     * @return 佣金账单
     */
    @Override
    public CommissionBill selectCommissionBillByBillId(Long billId)
    {
        return commissionBillMapper.selectCommissionBillByBillId(billId);
    }

    /**
     * 查询佣金账单列表
     * 
     * @param commissionBill 佣金账单
     * @return 佣金账单
     */
    @Override
    public List<CommissionBill> selectCommissionBillList(CommissionBill commissionBill)
    {
        return commissionBillMapper.selectCommissionBillList(commissionBill);
    }

    /**
     * 根据用户ID查询佣金账单列表
     * 
     * @param userId 用户ID
     * @return 佣金账单集合
     */
    @Override
    public List<CommissionBill> selectCommissionBillListByUserId(Long userId)
    {
        return commissionBillMapper.selectCommissionBillListByUserId(userId);
    }

    /**
     * 根据用户ID和年月查询佣金账单
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 佣金账单
     */
    @Override
    public CommissionBill selectCommissionBillByUserIdAndMonth(Long userId, Integer billYear, Integer billMonth)
    {
        return commissionBillMapper.selectCommissionBillByUserIdAndMonth(userId, billYear, billMonth);
    }

    /**
     * 新增佣金账单
     * 
     * @param commissionBill 佣金账单
     * @return 结果
     */
    @Override
    public int insertCommissionBill(CommissionBill commissionBill)
    {
        commissionBill.setCreateTime(DateUtils.getNowDate());
        return commissionBillMapper.insertCommissionBill(commissionBill);
    }

    /**
     * 修改佣金账单
     * 
     * @param commissionBill 佣金账单
     * @return 结果
     */
    @Override
    public int updateCommissionBill(CommissionBill commissionBill)
    {
        commissionBill.setUpdateTime(DateUtils.getNowDate());
        return commissionBillMapper.updateCommissionBill(commissionBill);
    }

    /**
     * 批量删除佣金账单
     * 
     * @param billIds 需要删除的佣金账单主键
     * @return 结果
     */
    @Override
    public int deleteCommissionBillByBillIds(Long[] billIds)
    {
        return commissionBillMapper.deleteCommissionBillByBillIds(billIds);
    }

    /**
     * 删除佣金账单信息
     * 
     * @param billId 佣金账单主键
     * @return 结果
     */
    @Override
    public int deleteCommissionBillByBillId(Long billId)
    {
        return commissionBillMapper.deleteCommissionBillByBillId(billId);
    }

    /**
     * 创建或更新月度账单
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 佣金账单
     */
    @Override
    @Transactional
    public CommissionBill createOrUpdateMonthlyBill(Long userId, String userName, Integer billYear, Integer billMonth)
    {
        CommissionBill bill = selectCommissionBillByUserIdAndMonth(userId, billYear, billMonth);
        if (bill == null) {
            bill = new CommissionBill();
            bill.setUserId(userId);
            bill.setUserName(userName);
            bill.setBillYear(billYear);
            bill.setBillMonth(billMonth);
            bill.setTotalIncome(BigDecimal.ZERO);
            bill.setTaskCommission(BigDecimal.ZERO);
            bill.setRecommendReward(BigDecimal.ZERO);
            bill.setOtherIncome(BigDecimal.ZERO);
            bill.setTotalWithdraw(BigDecimal.ZERO);
            bill.setWithdrawCount(0);
            insertCommissionBill(bill);
        }
        return bill;
    }

    /**
     * 更新任务佣金收入
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 佣金金额
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateTaskCommission(Long userId, String userName, BigDecimal amount)
    {
        Calendar calendar = Calendar.getInstance();
        int billYear = calendar.get(Calendar.YEAR);
        int billMonth = calendar.get(Calendar.MONTH) + 1;
        
        // 确保月度账单存在
        createOrUpdateMonthlyBill(userId, userName, billYear, billMonth);
        
        // 更新任务佣金
        return commissionBillMapper.updateTaskCommission(userId, billYear, billMonth, amount) > 0;
    }

    /**
     * 更新推荐奖励收入
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 奖励金额
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateRecommendReward(Long userId, String userName, BigDecimal amount)
    {
        Calendar calendar = Calendar.getInstance();
        int billYear = calendar.get(Calendar.YEAR);
        int billMonth = calendar.get(Calendar.MONTH) + 1;
        
        // 确保月度账单存在
        createOrUpdateMonthlyBill(userId, userName, billYear, billMonth);
        
        // 更新推荐奖励
        return commissionBillMapper.updateRecommendReward(userId, billYear, billMonth, amount) > 0;
    }

    /**
     * 更新其他收入
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 收入金额
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateOtherIncome(Long userId, String userName, BigDecimal amount)
    {
        Calendar calendar = Calendar.getInstance();
        int billYear = calendar.get(Calendar.YEAR);
        int billMonth = calendar.get(Calendar.MONTH) + 1;
        
        // 确保月度账单存在
        createOrUpdateMonthlyBill(userId, userName, billYear, billMonth);
        
        // 更新其他收入
        return commissionBillMapper.updateOtherIncome(userId, billYear, billMonth, amount) > 0;
    }

    /**
     * 更新提现金额和次数
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 提现金额
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateWithdrawAmount(Long userId, String userName, BigDecimal amount)
    {
        Calendar calendar = Calendar.getInstance();
        int billYear = calendar.get(Calendar.YEAR);
        int billMonth = calendar.get(Calendar.MONTH) + 1;
        
        // 确保月度账单存在
        createOrUpdateMonthlyBill(userId, userName, billYear, billMonth);
        
        // 更新提现金额和次数
        return commissionBillMapper.updateWithdrawAmount(userId, billYear, billMonth, amount) > 0;
    }

    /**
     * 更新总支出
     *
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 支出金额
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateTotalExpense(Long userId, String userName, BigDecimal amount)
    {
        Calendar calendar = Calendar.getInstance();
        int billYear = calendar.get(Calendar.YEAR);
        int billMonth = calendar.get(Calendar.MONTH) + 1;

        // 确保月度账单存在
        createOrUpdateMonthlyBill(userId, userName, billYear, billMonth);

        // 更新总支出
        return commissionBillMapper.updateTotalExpense(userId, billYear, billMonth, amount) > 0;
    }

    /**
     * 获取用户月度账单统计
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getMonthlyBillStatistics(Long userId, Integer billYear, Integer billMonth)
    {
        Map<String, Object> result = new HashMap<>();
        
        CommissionBill bill = selectCommissionBillByUserIdAndMonth(userId, billYear, billMonth);
        if (bill != null) {
            result.put("totalIncome", bill.getTotalIncome());
            result.put("taskCommission", bill.getTaskCommission());
            result.put("recommendReward", bill.getRecommendReward());
            result.put("otherIncome", bill.getOtherIncome());
            result.put("totalExpense", bill.getTotalExpense());
            result.put("totalWithdraw", bill.getTotalWithdraw());
            result.put("withdrawCount", bill.getWithdrawCount());
            result.put("netIncome", bill.getTotalIncome().subtract(bill.getTotalExpense()));
        } else {
            result.put("totalIncome", BigDecimal.ZERO);
            result.put("taskCommission", BigDecimal.ZERO);
            result.put("recommendReward", BigDecimal.ZERO);
            result.put("otherIncome", BigDecimal.ZERO);
            result.put("totalExpense", BigDecimal.ZERO);
            result.put("totalWithdraw", BigDecimal.ZERO);
            result.put("withdrawCount", 0);
            result.put("netIncome", BigDecimal.ZERO);
        }
        
        return result;
    }
}
