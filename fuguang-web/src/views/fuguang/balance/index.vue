<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>用户总数</span>
            <i class="el-icon-user"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statistics.userCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>总余额</span>
            <i class="el-icon-wallet"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ statistics.totalBalance || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="box-card">
          <div class="card-header">
            <span>累计收入</span>
            <i class="el-icon-plus"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ statistics.totalIncome || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="box-card">
          <div class="card-header">
            <span>累计支出</span>
            <i class="el-icon-minus"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ statistics.totalExpense || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="box-card">
          <div class="card-header">
            <span>累计提现</span>
            <i class="el-icon-wallet"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ statistics.totalWithdraw || 0 }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:balance:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="balanceList" @selection-change="handleSelectionChange">
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户昵称" align="center" prop="userName" />
      <el-table-column label="总余额" align="center" prop="totalBalance">
        <template slot-scope="scope">
          <span style="color: #409EFF;">¥{{ scope.row.totalBalance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="可用余额" align="center" prop="availableBalance">
        <template slot-scope="scope">
          <span style="color: #67C23A;">¥{{ scope.row.availableBalance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="冻结余额" align="center" prop="frozenBalance">
        <template slot-scope="scope">
          <span style="color: #F56C6C;">¥{{ scope.row.frozenBalance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计收入" align="center" prop="totalIncome">
        <template slot-scope="scope">
          <span style="color: #67C23A;">¥{{ scope.row.totalIncome }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计支出" align="center" prop="totalExpense">
        <template slot-scope="scope">
          <span style="color: #F56C6C;">¥{{ scope.row.totalExpense }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计提现" align="center" prop="totalWithdraw">
        <template slot-scope="scope">
          <span style="color: #909399;">¥{{ scope.row.totalWithdraw }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-money" @click="handleAdjust(scope.row)"
            v-hasPermi="['fuguang:balance:adjust']">调整</el-button>
          <el-button size="mini" type="text" icon="el-icon-lock" @click="handleFreeze(scope.row)"
            v-hasPermi="['fuguang:balance:freeze']">冻结</el-button>
          <el-button size="mini" type="text" icon="el-icon-unlock" @click="handleUnfreeze(scope.row)"
            v-hasPermi="['fuguang:balance:unfreeze']">解冻</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />


    <!-- 余额调整对话框 -->
    <el-dialog title="余额调整" :visible.sync="adjustOpen" width="400px" append-to-body>
      <el-form ref="adjustForm" :model="adjustForm" :rules="adjustRules" label-width="80px">
        <el-form-item label="用户昵称">
          <el-input v-model="adjustForm.userName" disabled />
        </el-form-item>
        <el-form-item label="当前余额">
          <el-input v-model="adjustForm.currentBalance" disabled />
        </el-form-item>
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="adjustForm.type">
            <el-radio label="increase">增加</el-radio>
            <el-radio label="decrease">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input v-model="adjustForm.amount" placeholder="请输入调整金额" />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="adjustForm.reason" type="textarea" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdjust">确 定</el-button>
        <el-button @click="adjustOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 冻结余额对话框 -->
    <el-dialog title="冻结余额" :visible.sync="freezeOpen" width="400px" append-to-body>
      <el-form ref="freezeForm" :model="freezeForm" :rules="freezeRules" label-width="80px">
        <el-form-item label="用户昵称">
          <el-input v-model="freezeForm.userName" disabled />
        </el-form-item>
        <el-form-item label="可用余额">
          <el-input v-model="freezeForm.availableBalance" disabled />
        </el-form-item>
        <el-form-item label="冻结金额" prop="amount">
          <el-input v-model="freezeForm.amount" placeholder="请输入冻结金额" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFreeze">确 定</el-button>
        <el-button @click="freezeOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 解冻余额对话框 -->
    <el-dialog title="解冻余额" :visible.sync="unfreezeOpen" width="400px" append-to-body>
      <el-form ref="unfreezeForm" :model="unfreezeForm" :rules="unfreezeRules" label-width="80px">
        <el-form-item label="用户昵称">
          <el-input v-model="unfreezeForm.userName" disabled />
        </el-form-item>
        <el-form-item label="冻结余额">
          <el-input v-model="unfreezeForm.frozenBalance" disabled />
        </el-form-item>
        <el-form-item label="解冻金额" prop="amount">
          <el-input v-model="unfreezeForm.amount" placeholder="请输入解冻金额" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUnfreeze">确 定</el-button>
        <el-button @click="unfreezeOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserBalance, getUserBalance, delUserBalance, addUserBalance, updateUserBalance, adjustBalance, freezeBalance, unfreezeBalance, getBalanceStatistics } from "@/api/fuguang/balance";

export default {
  name: "Balance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户余额表格数据
      balanceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示调整弹出层
      adjustOpen: false,
      // 是否显示冻结弹出层
      freezeOpen: false,
      // 是否显示解冻弹出层
      unfreezeOpen: false,
      // 统计数据
      statistics: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
      },
      // 表单参数
      form: {},
      // 调整表单参数
      adjustForm: {},
      // 冻结表单参数
      freezeForm: {},
      // 解冻表单参数
      unfreezeForm: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" }
        ],
      },
      // 调整表单校验
      adjustRules: {
        type: [
          { required: true, message: "调整类型不能为空", trigger: "change" }
        ],
        amount: [
          { required: true, message: "调整金额不能为空", trigger: "blur" },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
        ],
        reason: [
          { required: true, message: "调整原因不能为空", trigger: "blur" }
        ]
      },
      // 冻结表单校验
      freezeRules: {
        amount: [
          { required: true, message: "冻结金额不能为空", trigger: "blur" },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
        ]
      },
      // 解冻表单校验
      unfreezeRules: {
        amount: [
          { required: true, message: "解冻金额不能为空", trigger: "blur" },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询用户余额列表 */
    getList() {
      this.loading = true;
      listUserBalance(this.queryParams).then(response => {
        this.balanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getBalanceStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        balanceId: null,
        userId: null,
        userName: null,
        totalBalance: null,
        availableBalance: null,
        frozenBalance: null,
        totalIncome: null,
        totalExpense: null,
        totalWithdraw: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.balanceId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/balance/export', {
        ...this.queryParams
      }, `balance_${new Date().getTime()}.xlsx`)
    },
    /** 余额调整按钮操作 */
    handleAdjust(row) {
      this.adjustForm = {
        userId: row.userId,
        userName: row.userName,
        currentBalance: row.availableBalance,
        type: 'increase',
        amount: '',
        reason: ''
      };
      this.adjustOpen = true;
    },
    /** 提交调整 */
    submitAdjust() {
      this.$refs["adjustForm"].validate(valid => {
        if (valid) {
          adjustBalance(this.adjustForm).then(response => {
            this.$modal.msgSuccess("调整成功");
            this.adjustOpen = false;
            this.getList();
            this.getStatistics();
          });
        }
      });
    },
    /** 冻结余额按钮操作 */
    handleFreeze(row) {
      this.freezeForm = {
        userId: row.userId,
        userName: row.userName,
        availableBalance: row.availableBalance,
        amount: ''
      };
      this.freezeOpen = true;
    },
    /** 提交冻结 */
    submitFreeze() {
      this.$refs["freezeForm"].validate(valid => {
        if (valid) {
          freezeBalance(this.freezeForm).then(response => {
            this.$modal.msgSuccess("冻结成功");
            this.freezeOpen = false;
            this.getList();
            this.getStatistics();
          });
        }
      });
    },
    /** 解冻余额按钮操作 */
    handleUnfreeze(row) {
      this.unfreezeForm = {
        userId: row.userId,
        userName: row.userName,
        frozenBalance: row.frozenBalance,
        amount: ''
      };
      this.unfreezeOpen = true;
    },
    /** 提交解冻 */
    submitUnfreeze() {
      this.$refs["unfreezeForm"].validate(valid => {
        if (valid) {
          unfreezeBalance(this.unfreezeForm).then(response => {
            this.$modal.msgSuccess("解冻成功");
            this.unfreezeOpen = false;
            this.getList();
            this.getStatistics();
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.box-card {
  height: 100px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.card-header i {
  font-size: 20px;
}

.card-content {
  margin-top: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
</style>
