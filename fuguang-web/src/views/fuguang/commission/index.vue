<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>活跃用户</span>
            <i class="el-icon-user" style="color: #409EFF;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.activeUserCount || 0 }}</div>
            <div class="card-sub">当月活跃用户</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>总收入</span>
            <i class="el-icon-plus" style="color: #67C23A;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.totalIncome || 0 }}</div>
            <div class="card-sub">当月总收入</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="box-card">
          <div class="card-header">
            <span>总支出</span>
            <i class="el-icon-minus" style="color: #F56C6C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.totalExpense || 0 }}</div>
            <div class="card-sub">当月总支出</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="box-card">
          <div class="card-header">
            <span>总提现</span>
            <i class="el-icon-wallet" style="color: #909399;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.totalWithdraw || 0 }}</div>
            <div class="card-sub">当月总提现</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="box-card">
          <div class="card-header">
            <span>净收入</span>
            <i class="el-icon-money" style="color: #E6A23C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.netIncome || 0 }}</div>
            <div class="card-sub">当月净收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="账单年份" prop="billYear">
        <el-select v-model="queryParams.billYear" placeholder="请选择年份" clearable>
          <el-option v-for="year in yearOptions" :key="year" :label="year + '年'" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="账单月份" prop="billMonth">
        <el-select v-model="queryParams.billMonth" placeholder="请选择月份" clearable>
          <el-option v-for="month in monthOptions" :key="month" :label="month + '月'" :value="month" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:commission:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="commissionList" @selection-change="handleSelectionChange">
      <el-table-column label="用户信息" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.userName }}</div>
          <div style="color: #909399; font-size: 12px;">ID: {{ scope.row.userId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="账单月份" align="center" width="110">
        <template slot-scope="scope">
          <el-tag type="primary">{{ scope.row.billYear }}年{{ scope.row.billMonth }}月</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总收入" align="center" prop="totalIncome">
        <template slot-scope="scope">
          <span style="color: #67C23A; font-weight: bold;">¥{{ scope.row.totalIncome }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务佣金" align="center" prop="taskCommission">
        <template slot-scope="scope">
          <span>¥{{ scope.row.taskCommission }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐奖励" align="center" prop="recommendReward">
        <template slot-scope="scope">
          <span>¥{{ scope.row.recommendReward }}</span>
        </template>
      </el-table-column>
      <el-table-column label="其他收入" align="center" prop="otherIncome">
        <template slot-scope="scope">
          <span>¥{{ scope.row.otherIncome }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总支出" align="center" prop="totalExpense">
        <template slot-scope="scope">
          <span style="color: #F56C6C; font-weight: bold;">¥{{ scope.row.totalExpense }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总提现" align="center" prop="totalWithdraw">
        <template slot-scope="scope">
          <span style="color: #909399;">¥{{ scope.row.totalWithdraw }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提现次数" align="center" prop="withdrawCount" />
      <el-table-column label="净收入" align="center">
        <template slot-scope="scope">
          <span style="color: #409EFF; font-weight: bold;">¥{{ (scope.row.totalIncome -
            scope.row.totalExpense).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['fuguang:commission:query']">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />



    <!-- 查看详情对话框 -->
    <el-dialog title="账单详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户信息">{{ viewData.userName }} (ID: {{ viewData.userId }})</el-descriptions-item>
        <el-descriptions-item label="账单月份">{{ viewData.billYear }}年{{ viewData.billMonth }}月</el-descriptions-item>
        <el-descriptions-item label="总收入">¥{{ viewData.totalIncome }}</el-descriptions-item>
        <el-descriptions-item label="任务佣金">¥{{ viewData.taskCommission }}</el-descriptions-item>
        <el-descriptions-item label="推荐奖励">¥{{ viewData.recommendReward }}</el-descriptions-item>
        <el-descriptions-item label="其他收入">¥{{ viewData.otherIncome }}</el-descriptions-item>
        <el-descriptions-item label="总支出">¥{{ viewData.totalExpense }}</el-descriptions-item>
        <el-descriptions-item label="总提现">¥{{ viewData.totalWithdraw }}</el-descriptions-item>
        <el-descriptions-item label="提现次数">{{ viewData.withdrawCount }}次</el-descriptions-item>
        <el-descriptions-item label="净收入">¥{{ (viewData.totalIncome - viewData.totalExpense).toFixed(2)
          }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewData.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewData.updateTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCommissionBill, getCommissionBill, delCommissionBill, addCommissionBill, updateCommissionBill, getCommissionOverview } from "@/api/fuguang/commission";

export default {
  name: "Commission",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 佣金账单表格数据
      commissionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 查看数据
      viewData: {},
      // 统计概览数据
      overview: {},
      // 年份选项
      yearOptions: [],
      // 月份选项
      monthOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        billYear: null,
        billMonth: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" }
        ],
        billYear: [
          { required: true, message: "账单年份不能为空", trigger: "change" }
        ],
        billMonth: [
          { required: true, message: "账单月份不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.initYearOptions();
    this.getList();
    this.getOverview();
  },
  methods: {
    /** 初始化年份选项 */
    initYearOptions() {
      const currentYear = new Date().getFullYear();
      for (let i = currentYear; i >= currentYear - 5; i--) {
        this.yearOptions.push(i);
      }
    },
    /** 查询佣金账单列表 */
    getList() {
      this.loading = true;
      listCommissionBill(this.queryParams).then(response => {
        this.commissionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计概览 */
    getOverview() {
      getCommissionOverview().then(response => {
        this.overview = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        billId: null,
        userId: null,
        userName: null,
        billYear: null,
        billMonth: null,
        totalIncome: 0,
        taskCommission: 0,
        recommendReward: 0,
        otherIncome: 0,
        totalWithdraw: 0,
        withdrawCount: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.billId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewData = row;
      this.viewOpen = true;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/commission/export', {
        ...this.queryParams
      }, `commission_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.box-card {
  height: 100px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.card-header i {
  font-size: 20px;
}

.card-content {
  margin-top: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-sub {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
